export default [
  // {
  //   path: 'roundup',
  //   name: 'rRoundupsLanding',
  //   component: () => import('src/concerns/hpg/pages/HpgHomePage2025.vue'),
  // },
  // {
  //   path: 'creat-one-off-game',
  //   name: 'rCreateOneOffGame',
  //   component: () => import('src/concerns/hpg/pages/HpgHomePage2025.vue'),
  // },
  {
    path: 'roundup/v/:gameSlug',
    name: 'rRoundupGame',
    component: () =>
      import('src/concerns/realty-game/layouts/one-off/OneOffGameLayout.vue'),
    meta: {
      title: 'Property Price Challenge - Interactive Real Estate Game',
      description:
        'Test your property market knowledge with our interactive price guessing game. Challenge yourself with real properties and see how well you know local values.',
      keywords:
        'property price game, real estate challenge, property valuation, house price quiz, market knowledge test',
      ogType: 'website',
      ogImage:
        'https://assets.propertysquares.com/staticfiles/ppsqs/house_price_guess_on_map_concept.jpeg',
      twitterCard: 'summary_large_image',
    },
    children: [
      {
        path: '',
        name: 'rRoundupGameStart',
        component: () =>
          import('src/concerns/realty-game/pages/one-off/OneOffGameStartPage.vue'),
        meta: {
          title:
            'Start Property Price Challenge - Test Your Market Knowledge',
          description:
            'Ready to test your property market knowledge? Start our interactive price guessing game and see how well you know local property values.',
          keywords:
            'start property game, price challenge, real estate quiz, property knowledge test',
          ogType: 'website',
        },
      },
      {
        path: 'superwiser',
        name: 'rRoundupGamePropertiesAdmin',
        // june 2025: not convinced below does anything:
        meta: {
          ssr: false, // Exclude from SSR
          title: 'Game Properties Admin - Property Management',
          description:
            'Admin interface for managing game properties and settings.',
          robots: 'noindex, nofollow',
        },
        component: () =>
          import(
            'src/concerns/realty-game/pages/one-off/OneOffGamePropertiesAdmin.vue'
          ),
      },
      // {
      //   path: 'admin/create-game',
      //   name: 'rRoundupGameCreateGame',
      //   component: () =>
      //     import('src/concerns/realty-game/pages/one-off/OneOffGameCreateGame.vue'),
      //   meta: {
      //     title: 'Create New Property Game - Game Builder',
      //     description:
      //       'Create a new property price guessing game with custom properties and settings.',
      //     robots: 'noindex, nofollow',
      //   },
      // },
      {
        path: 'property/:propertyUuid',
        name: 'rRoundupGameProperty',
        component: () =>
          import(
            'src/concerns/realty-game/pages/one-off/OneOffGamePropertyPage.vue'
          ),
        props: (route) => ({
          propertyUuid: route.params.propertyUuid,
          gameSessionId: route.query.session,
        }),
        meta: {
          title: 'Guess Property Price - Property Price Challenge',
          description:
            "Make your best guess at this property's value. Test your knowledge of the local property market.",
          keywords:
            'property price guess, house valuation, property game, real estate quiz',
          ogType: 'website',
          robots: 'noindex, nofollow', // Game session specific
        },
      },
      // {
      //   path: 'results_older/:gameSessionId',
      //   name: 'rRoundupGameResultsOlder',
      //   component: () =>
      //     import(
      //       'src/concerns/realty-game/pages/one-off/OneOffGameResultsPage.vue'
      //     ),
      // },
      {
        path: 'results/:gameSessionId',
        name: 'rRoundupGameResults',
        component: () =>
          import(
            'src/concerns/realty-game/pages/one-off/OneOffGameResultsSummaryPage.vue'
          ),
        props: true,
        meta: {
          title: 'Your Property Price Challenge Results',
          description:
            'See how well you performed in the property price challenge. Share your results with friends!',
          keywords:
            'property game results, price guess results, property knowledge score',
          ogType: 'website',
          ogImage:
            'https://assets.propertysquares.com/staticfiles/ppsqs/house_price_guess_on_map_concept.jpeg',
          robots: 'noindex, nofollow', // Game session specific
        },
      },
      {
        path: 'results_to_share/:gameSessionId',
        name: 'rRoundupGameResultsShareable',
        component: () =>
          import(
            'src/concerns/realty-game/pages/one-off/OneOffGameResultsShareablePage.vue'
          ),
        props: true,
        meta: {
          title: 'Property Price Challenge Results - Shareable',
          description:
            'Property price challenge results without revealing actual prices. Perfect for sharing with friends!',
          keywords:
            'property game results, price guess results, shareable results, property knowledge',
          ogType: 'website',
          ogImage:
            'https://assets.propertysquares.com/staticfiles/ppsqs/house_price_guess_on_map_concept.jpeg',
          robots: 'noindex, nofollow', // Game session specific
        },
      },
      {
        path: 'detailed_results/:gameSessionId',
        name: 'rRoundupGameResultsDetailed',
        component: () =>
          import(
            'src/concerns/realty-game/pages/one-off/OneOffGameResultsDetailedPage.vue'
          ),
        props: true,
        meta: {
          title: 'Property Price Challenge - Detailed Results',
          description:
            'Complete property price challenge results with all prices and detailed breakdown.',
          keywords:
            'property game results, price guess results, detailed results, property prices',
          ogType: 'website',
          ogImage:
            'https://assets.propertysquares.com/staticfiles/ppsqs/house_price_guess_on_map_concept.jpeg',
          robots: 'noindex, nofollow', // Game session specific
        },
      },
    ],
  },
]; 